import os
import re
import requests
import csv
import logging
# Configure logging
logging.basicConfig(level=logging.INFO)

from datetime import datetime, timezone
from opensearchpy import OpenSearch
from vertexai.language_models import TextEmbeddingModel

from PyPDF2 import PdfReader
import docx  # For handling .docx files
import boto3  # For AWS S3
import re

from google.oauth2 import service_account
from helper_functions import initialize_opensearch_client

credentials = service_account.Credentials.from_service_account_file(os.getenv("GOOGLE_APPLICATION_CREDENTIALS"))


# AWS OpenSearch and S3 credentials
AWS_ACCESS_KEY = os.environ.get("AWS_ACCESS_KEY")
AWS_SECRET_KEY = os.environ.get("AWS_SECRET_KEY")
AWS_REGION = os.environ.get("AWS_REGION")
S3_BUCKET_NAME = os.environ.get("S3_BUCKET_NAME")
embedding_model = os.environ.get("GOOGLE-EMBEDDING-MODEL")



# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
    region_name=AWS_REGION
)

# to test locally
# def normalize_to_s3_path(general_path: str) -> str:
#     parts = general_path.strip().split("\\")
#     logging.info(parts)
#     if len(parts) < 2:
#         raise ValueError("Path must contain at least bucket and filename")
#     bucket = parts[0]
#     key = "/".join(parts[1:])
#     return f"s3://{bucket}/{key}"


def generate_embedding(text):
    model = TextEmbeddingModel.from_pretrained(embedding_model)
    embedding = model.get_embeddings([text])[0]
    return embedding.values

# Chunk text into manageable sizes for embedding
def chunk_text(text, max_token_length=8192):
    words = text.split()
    chunks = []
    current_chunk = []
    current_length = 0

    for word in words:
        word_length = len(word) + 1  # Include space
        if current_length + word_length > max_token_length:
            chunks.append(" ".join(current_chunk))
            current_chunk = []
            current_length = 0
        current_chunk.append(word)
        current_length += word_length

    if current_chunk:
        chunks.append(" ".join(current_chunk))

    return chunks

# Extract text from a PDF file
def extract_text_from_pdf(file_path):
    reader = PdfReader(file_path)
    return "\n".join([page.extract_text() for page in reader.pages])

# Check if PDF exceeds the allowed token limit
def is_pdf_too_large(file_path, token_limit=8192):
    text = extract_text_from_pdf(file_path)
    words = text.split()
    total_tokens = len(words)  # Estimate tokens based on word count
    return total_tokens > token_limit

# Upload documents to OpenSearch
def upload_documents(documents):
    try:
        valid_documents = [doc for doc in documents if "text" in doc]
        logging.info(f"Uploading {len(valid_documents)} documents to OpenSearch...")
        opensearch_client = initialize_opensearch_client()
        for doc in valid_documents:
            try:
                # # Index the document in OpenSearch
                opensearch_client.index(
                    index="index-hr-policies-dev-dummy",
                    body=doc,
                    id=doc["id"]
                )
                logging.info(f"uploaded successfully {doc['id']}")
            except Exception as e:
                logging.error(f"Error indexing document {doc['id']}: {e}")
                
        # logging.info(f"{len(valid_documents} Documents uploaded successfully.")
    except Exception as e:
        logging.error(f"Error uploading documents: {e}")

# Process a PDF file, chunk if necessary
def process_pdf(file_path, file_name, legal_entity, country,s3_path):  #EDITED
    try:
        # Check if PDF is too large
        if is_pdf_too_large(file_path):
            logging.info(f"PDF is too large, chunking and processing: {file_path}")
            text_content = extract_text_from_pdf(file_path)
            chunks = chunk_text(text_content)

            documents = []
            for i, chunk in enumerate(chunks):
                try:
                    embeddings = generate_embedding(chunk)
                    document_id = re.sub(r'[^a-zA-Z0-9_=]', '_', file_name)  # Replace invalid characters with '_'

                    document = {
                        "id": document_id,
                        "title": f"{file_name} - Chunk {i}",
                        "text": chunk,
                        "department": "Policies",
                        "filepath": s3_path,
                        # to test locally
                        # "filepath": normalize_to_s3_path(s3_path.replace("D:\WagonHr\\","")),
                        "date_created": datetime.now(timezone.utc).isoformat(timespec='seconds'),
                        "embedding": embeddings,
                        "country": [country],  
                        "legal_entity": [legal_entity],  
                    }
                    documents.append(document)
                except Exception as e:
                    logging.info(f"Error processing chunk {i}: {e}")

            upload_documents(documents)
        else:
            # Process normally if the PDF is not too large
            logging.info(f"Processing normal PDF (no chunking): {file_path}")
            text_content = extract_text_from_pdf(file_path)
            embeddings = generate_embedding(text_content)

            document_id = re.sub(r'[^a-zA-Z0-9_=]', '_', file_name)  # Replace invalid characters with '_'

            document = {
                "id": document_id,
                "title": file_name,
                "text": text_content,
                "department": "Policies",
                "filepath": s3_path,
                # to test locally
                # "filepath": normalize_to_s3_path(s3_path.replace("D:\WagonHr\\","")),
                "date_created": datetime.now(timezone.utc).isoformat(timespec='seconds'),
                "embedding": embeddings,
                "country": [country],  #EDITED
                "legal_entity": [legal_entity],  #EDITED
            }
            upload_documents([document])

    except Exception as e:
        logging.info(f"Error processing file {file_name}: {e}")


# Extract text from a Word document
def extract_text_from_docx(file_path):
    doc = docx.Document(file_path)
    return "\n".join([para.text for para in doc.paragraphs])

# Process and upload files from S3
def process_and_upload_s3_bucket(folder_to_index=None, files_to_index=None):
    try:
        # List objects in the S3 bucket
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=S3_BUCKET_NAME, Prefix='policies/')

        for page in pages:
            if 'Contents' not in page:
                continue

            for obj in page['Contents']:
                key = obj['Key']
                
                # Skip if not in the policies folder
                if not key.startswith('policies/'):
                    continue

                # Parse folder and file name
                parts = key.split('/')
                if len(parts) != 3:  # policies/<folder>/<file>
                    continue

                folder_name = parts[1]  # Get the folder name (legal_entity)
                file_name = parts[2]  # Get the actual file name
        ### to test locally
        # folder_path = r"D:\WagonHr\Policies\IN-MT-001"
        # # List all files (excluding directories)
        # files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
        # # logging.info(files)
        # for file_name in files:
        #     if file_name.endswith(('.pdf', '.docx')):
        #         logging.info(f"Processing file: {file_name}")

                # Filter by folder name if provided
                if folder_name and folder_to_index and folder_name != folder_to_index:
                    continue
                
                # Filter by file name if specific file names are provided
                if file_name and files_to_index and file_name not in files_to_index:
                    continue

                # s3_path = os.path.join(folder_path, file_name)
                s3_path = f"{S3_BUCKET_NAME}/{key}"

                # Determine legal_entity and country
                legal_entity = folder_name
                country = legal_entity.split('-')[0]

                # to test locally
                # legal_entity = folder_path.split('/')[-1].split("\\")[-1]
                # country = legal_entity.split('-')[0]
                
                # # Download the file from S3
                try:
                    response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=key)
                    file_content = response['Body'].read()
                    
                    # Create /tmp directory if it doesn't exist
                    if not os.path.exists('/tmp'):
                        os.makedirs('/tmp')
                        
                    temp_file_path = f"/tmp/{file_name}"

                    with open(temp_file_path, 'wb') as f:
                        f.write(file_content)

                    # Process the file based on its type
                    if file_name.endswith(".pdf"):
                        process_pdf(temp_file_path, file_name, legal_entity, country, s3_path)
                    elif file_name.endswith(".docx"):
                        text_content = extract_text_from_docx(temp_file_path)
                    # to test locally
                    # # Process the file based on its type
                    # if file_name.endswith(".pdf"):
                    #     process_pdf(s3_path, file_name, legal_entity, country, s3_path)
                    # elif file_name.endswith(".docx"):
                        # text_content = extract_text_from_docx(s3_path)
                        embeddings = generate_embedding(text_content)

                        document_id = re.sub(r'[^a-zA-Z0-9_=]', '_', file_name)

                        document = {
                            "id": document_id,
                            "title": file_name,
                            "text": text_content,
                            "department": "Policies",
                            "filepath": s3_path,
                            # to test locally
                            # "filepath": normalize_to_s3_path(s3_path.replace("D:\WagonHr\\","")),
                            "date_created": datetime.now(timezone.utc).isoformat(timespec='seconds'),
                            "embedding": embeddings,
                            "country": [country],
                            "legal_entity": [legal_entity],
                        }
                        upload_documents([document])
                    else:
                        logging.info(f"Skipping unsupported file type: {file_name}")

                    # # Clean up temporary file
                    os.remove(temp_file_path)

                except Exception as e:
                    logging.error(f"Error processing file {file_name}: {e}")

    except Exception as e:
        logging.error(f"Error accessing S3 bucket: {e}")

# Main function
if __name__ == "__main__":
    process_and_upload_s3_bucket()
