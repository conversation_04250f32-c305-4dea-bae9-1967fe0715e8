from dotenv import load_dotenv
load_dotenv()
import json
from Scripts.indexation import process_and_upload_s3_bucket


def index_documents(folder_name, file_names):
    # Extract `folder_name` and `file_names`
    # folder_name = req_body.get("folder_name")
    # file_names = req_body.get("file_names")
    file_names_list = [name.strip() for name in file_names.split(",")] if file_names else None

    # Call the processing function
    if folder_name and file_names_list:
        # Process specific files in a specific folder
        process_and_upload_s3_bucket(folder_to_index=folder_name, files_to_index=file_names_list)
    elif folder_name:
        # Process all files in the specific folder
        process_and_upload_s3_bucket(folder_to_index=folder_name)
    else:
        # Process all files in all folders
        process_and_upload_s3_bucket()

    # Return success response
    response =  json.dumps({"message": "Files processed successfully!", "status_code":200, "mimetype":"application/json"})
    # response = add_security_headers(response)
    return response
